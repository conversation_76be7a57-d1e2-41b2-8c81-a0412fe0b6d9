import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { HiAcademicCap, HiBriefcase, HiLightBulb, HiOfficeBuilding, HiOutlineVideoCamera, HiStar, HiUserGroup, HiEye, HiVideoCamera, HiUpload, HiDocument } from "react-icons/hi";
import { X, Video, Upload, FileText, Sparkles, CheckCircle } from "lucide-react";
import ResumePreview from "components/ResumePreview";
import Cookies from "js-cookie";
import TermsAndConditionsSection from "../TermsAndConditionsSection";

const CreateVR = ({ togglePopupVR }) => {
  const navigate = useNavigate();
  const [step, setStep] = useState(0); // Start with step 0 for resume selection

  const candId = Cookies.get("candId"); // Retrieve the candId from the cookie

  // State for existing video resumes
  const [existingResumes, setExistingResumes] = useState([]);
  const [selectedResumeId, setSelectedResumeId] = useState("");
  const [isLoadingResumes, setIsLoadingResumes] = useState(false);

  // State for resume data
  const [resumeData, setResumeData] = useState(null);
  const [showResumePreview, setShowResumePreview] = useState(false);
  const [completeResumeData, setCompleteResumeData] = useState(null);
  const [hasAutoSelected, setHasAutoSelected] = useState(false);

  // Add state for uploading new resume
  const [isUploadingResume, setIsUploadingResume] = useState(false);

  const saveqnscookie = (data) => {
    try {
      if (!data || !data.questions) {
        throw new Error("Invalid data format: missing questions");
      }
      
      const cookieData = JSON.stringify(data);
      
      Cookies.set("CreateVRres", cookieData, { expires: 7 }); // Expires in 7 days
      
      // Verify the data was saved correctly
      const savedData = Cookies.get("CreateVRres");
      if (!savedData) {
        throw new Error("Failed to save questions to cookie");
      }
    } catch (err) {
      console.error("Error saving to cookie:", err);
      throw err;
    }
  };

  const [formData, setFormData] = useState({
    candId: candId,
    jobRole: "",
    skills: [],
    companyType: "",
    experience: "",
    salary: {
      current: "",
      expected: "",
    },
  });
  const [skillInput, setSkillInput] = useState("");
  const [jobRoleInput, setJobRoleInput] = useState("");
  const [jobRoleSuggestions, setJobRoleSuggestions] = useState([]);
  const [skillSuggestions, setSkillSuggestions] = useState([]);
  const [allDatabaseSkills, setAllDatabaseSkills] = useState([]); // Store original database skills
  const [showJobRoleSuggestions, setShowJobRoleSuggestions] = useState(false);
  const [showSkillSuggestions, setShowSkillSuggestions] = useState(false);
  const formRef = useRef(null);
  const skillRef = useRef(null);
  const jobRef = useRef(null);
  const [error, setError] = useState(""); // Error state
  const [isLoading, setIsLoading] = useState(false); // Add loading state
  const [secondarySkillSuggestions, setSecondarySkillSuggestions] = useState([]); // For resume-derived skills

  // Enhanced skills extraction function with primary skills prioritization
  const extractSkillsFromResumeData = (resumeData) => {
    if (!resumeData || !resumeData.skills) {
      return [];
    }

    // Check if resume has enhanced skills structure
    if (resumeData.skills.primary_skills && Array.isArray(resumeData.skills.primary_skills)) {
      // Use primary skills (top 5) as default selection
      return resumeData.skills.primary_skills.slice(0, 5);
    }

    // Backward compatibility: if skills is still an array
    if (Array.isArray(resumeData.skills)) {
      return resumeData.skills.slice(0, 5); // Limit to 5 for consistency
    }

    // Fallback: try to get all_skills for backward compatibility
    if (resumeData.skills.all_skills && Array.isArray(resumeData.skills.all_skills)) {
      return resumeData.skills.all_skills.slice(0, 5);
    }

    return [];
  };

  // Extract secondary skills for suggestions
  const extractSecondarySkillsFromResumeData = (resumeData) => {
    if (!resumeData || !resumeData.skills) {
      return [];
    }

    // Get secondary skills if available
    if (resumeData.skills.secondary_skills && Array.isArray(resumeData.skills.secondary_skills)) {
      return resumeData.skills.secondary_skills;
    }

    // Fallback: get remaining skills from all_skills that aren't in primary
    if (resumeData.skills.all_skills && Array.isArray(resumeData.skills.all_skills) &&
        resumeData.skills.primary_skills && Array.isArray(resumeData.skills.primary_skills)) {
      return resumeData.skills.all_skills.filter(skill =>
        !resumeData.skills.primary_skills.includes(skill)
      );
    }

    return [];
  };

  const fetchJobRoles = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-roles`);
      const data = await response.json();
  
      if (data && Array.isArray(data.roles)) {
        return data.roles.map((role) => role.role_name);
      } else {
        console.warn("Roles data is not in the expected format.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    }
  };
  
  const fetchSkills = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-skills`);
      const data = await response.json();
  
      if (data && Array.isArray(data.skills)) {
        return data.skills.map((skill) => skill.skill_name);
      } else {
        console.warn("Skills data is not in the expected format.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching skills:", error);
      return [];
    }
  };
  
  const fetchJobRolesAndSkills = async () => {
    try {
      // Fetch roles and skills in parallel
      const [roleNames, skillNames] = await Promise.all([fetchJobRoles(), fetchSkills()]);

      // Update state with the fetched data
      setJobRoleSuggestions(roleNames || []);
      setAllDatabaseSkills(skillNames || []); // Store original database skills
      setSkillSuggestions(skillNames || []); // Initialize with database skills

      if (roleNames.length === 0) console.warn("No roles found.");
      if (skillNames.length === 0) console.warn("No skills found.");

      // Show error only if both fetches failed
      if (roleNames.length === 0 && skillNames.length === 0) {
        throw new Error("Failed to fetch both roles and skills");
      }
    } catch (error) {
      console.error("Fetch error:", error);
      toast.error("Failed to load roles and skills");
    }
  };
  
  
  // Fetch existing video resumes for the candidate
  const fetchExistingResumes = async () => {
    try {
      setIsLoadingResumes(true);
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${candId}`);

      if (response.ok) {
        const data = await response.json();
        setExistingResumes(data);
      } else {
        console.error("Failed to fetch existing resumes");
        setExistingResumes([]);
      }
    } catch (error) {
      console.error("Error fetching existing resumes:", error);
      setExistingResumes([]);
    } finally {
      setIsLoadingResumes(false);
    }
  };

  // Fetch candidate resume data
  const fetchResumeData = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`);

      if (response.ok) {
        const data = await response.json();
        const strippedResume = data.candidateProfile?.[0]?.stripped_resume;

        if (strippedResume) {
          setResumeData(strippedResume);
          setCompleteResumeData(strippedResume);

          // Auto-select the default resume option
          if (!hasAutoSelected) {
            setSelectedResumeId("default");
            setHasAutoSelected(true);
          }
        }
      } else {
        console.error("Failed to fetch resume data");
      }
    } catch (error) {
      console.error("Error fetching resume data:", error);
    }
  };

  useEffect(() => {
    fetchJobRolesAndSkills();
    fetchExistingResumes();
    fetchResumeData();
  }, [candId]);

 
  // Handle Job Role input
  const handleJobRoleInput = (e) => {
    const value = e.target.value.trim().toLowerCase(); // Normalize input
    setJobRoleInput(e.target.value); // Update the raw input value
    setFormData({ ...formData, jobRole: e.target.value }); // Update form data

    if (value === "") {
      // If the input is empty, show the full list of suggestions
      setShowJobRoleSuggestions(false);
      setJobRoleSuggestions([]);
    } else {
      // Filter from the original list of suggestions
      const filteredSuggestions = jobRoleSuggestions.filter((role) =>
        role.toLowerCase().includes(value)
      );

      setShowJobRoleSuggestions(filteredSuggestions.length > 0); // Show suggestions if there are matches
      setJobRoleSuggestions(filteredSuggestions); // Update suggestions list
      setShowSkillSuggestions(false); // Hide skill suggestions
    }
  };

  // Handle job role blur - trigger AI auto-selection when user finishes typing
  const handleJobRoleBlur = () => {
    if (formData.jobRole && formData.jobRole.trim().length > 0) {
      // Trigger AI auto-selection after a short delay to allow for suggestion clicks
      setTimeout(() => {
        autoSelectSkillsForJobRole(formData.jobRole);
      }, 300);
    }
  };

  const handleJobRoleSuggestionClick = (suggestion) => {
    setJobRoleInput(suggestion);
    setFormData({ ...formData, jobRole: suggestion });
    setShowJobRoleSuggestions(false);

    // Trigger AI auto-selection of skills for the selected job role
    autoSelectSkillsForJobRole(suggestion);
  };

  // Handle Skill input - show only database skills in dropdown
  const handleSkillInput = (e) => {
    const value = e.target.value.trim().toLowerCase(); // Normalize input
    setSkillInput(e.target.value); // Update the raw input value

    if (value === "") {
      // If the input is empty, hide suggestions
      setShowSkillSuggestions(false);
      setSkillSuggestions([]);
    } else {
      // Filter only from database skills, excluding already selected skills
      const filteredDatabaseSkills = allDatabaseSkills.filter(
        (skill) =>
          skill.toLowerCase().includes(value) &&
          !formData.skills.includes(skill) // Exclude already selected skills
      );

      setShowSkillSuggestions(filteredDatabaseSkills.length > 0);
      setSkillSuggestions(filteredDatabaseSkills); // Update suggestions with filtered database skills
      setShowJobRoleSuggestions(false); // Hide job role suggestions
    }
  };

  const handleSkillSuggestionClick = (suggestion) => {
    setSkillInput(""); // Clear the input after selection
    setFormData({
      ...formData,
      skills: [...formData.skills, suggestion], // Add the selected skill
    });
    setShowSkillSuggestions(false); // Hide suggestions
  };


  // Enhanced addSkill function - unlimited skill selection
  const addSkill = (skill) => {
    if (!formData.skills.includes(skill)) {
      setFormData({
        ...formData,
        skills: [...formData.skills, skill],
      });
      setSkillInput("");
      setShowSkillSuggestions(false);
    }
  };

  const removeSkill = (skillToRemove) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter((skill) => skill !== skillToRemove),
    });
  };

  // Clear all selected skills function
  const clearAllSkills = () => {
    setFormData({
      ...formData,
      skills: [],
    });
    setSkillInput("");
    setShowSkillSuggestions(false);
    // Reset any AI-selected skill indicators
    setHasAutoSelected(false);
  };

  // AI auto-selection of skills based on job role
  const autoSelectSkillsForJobRole = async (jobRole) => {
    if (!jobRole || jobRole.trim().length === 0) {
      return;
    }

    try {
      console.log(`Auto-selecting skills for job role: ${jobRole}`);

      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/recommend-skills`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jobRole: jobRole.trim() }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.skills && Array.isArray(data.skills) && data.skills.length === 5) {
        // Auto-select the 5 AI-recommended skills
        setFormData(prev => ({
          ...prev,
          skills: [...data.skills], // Replace with AI-selected skills
        }));

        setHasAutoSelected(true);
        console.log('Auto-selected skills:', data.skills);
        toast.success(`Auto-selected 5 relevant skills for ${jobRole}`);
      } else {
        console.warn('Invalid skills data received from API:', data);
      }
    } catch (error) {
      console.error('Error auto-selecting skills:', error);
      // Don't show error toast to avoid disrupting user experience
      // Just log the error and continue
    }
  };

  const handleFinalSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true); // Show the loader
    console.log("Form submitted:", formData);

    // Check if user selected an existing resume or default resume
    if (selectedResumeId && selectedResumeId !== "new") {
      if (selectedResumeId === "default") {
        // Use the complete resume data from uploaded resume
        const profileDataWithCompleteResume = {
          ...formData,
          completeResumeData: completeResumeData // Include complete resume data for AI processing
        };

        console.log("Using default resume with complete data:", profileDataWithCompleteResume);
        createVideoProfile(profileDataWithCompleteResume);
        return;
      } else {
        // Use existing video resume data
        const selectedResume = existingResumes.find(resume => resume.id.toString() === selectedResumeId);
        if (selectedResume) {
          const existingResumeData = {
            candId: candId,
            jobRole: selectedResume.role,
            skills: selectedResume.skills ? selectedResume.skills.split(", ") : [],
            companyType: selectedResume.job_type || "",
            experience: selectedResume.experience_range || "",
            salary: selectedResume.salary ? JSON.parse(selectedResume.salary) : { current: "", expected: "" },
          };

          console.log("Using existing video resume data:", existingResumeData);
          createVideoProfile(existingResumeData);
          return;
        }
      }
    }

    // Create new video profile with form data
    console.log("Creating new video profile with form data:", formData);
    createVideoProfile(formData);
  };

  const createVideoProfile = (profileData) => {
    const url = `${import.meta.env.VITE_APP_HOST}/api/v1/create-video-resume`;
    console.log("Post method to", url);

    // Prepare data for API call - exclude completeResumeData from API payload but store it locally
    const apiPayload = { ...profileData };
    delete apiPayload.completeResumeData;

    // Store complete resume data for future AI processing
    if (profileData.completeResumeData) {
      localStorage.setItem("completeResumeData", JSON.stringify(profileData.completeResumeData));
      console.log("Stored complete resume data for AI processing:", profileData.completeResumeData);
    }

    // Make an API call using fetch
    fetch(url, {
      method: "POST", // HTTP method
      headers: {
        "Content-Type": "application/json", // Specify JSON format
      },
      body: JSON.stringify(apiPayload), // Convert apiPayload to JSON string
    })
      .then((response) => {
        const data = response.json().then((data) => {
          if (!response.ok) {
            toast.error(data.message);
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return data; // Parsed JSON response
        });
        return data
      })
      .then((data) => {
        // Add detailed logging
        console.log("API Response:", data);
        console.log("Questions from API:", data.questions);

        // Validate questions array before saving
        if (!data.questions || !Array.isArray(data.questions) || data.questions.length === 0) {
          throw new Error("No questions received from API");
        }

        localStorage.setItem("formData", JSON.stringify(profileData));
        saveqnscookie(data); // save questions and videoProfileId

        navigate(`/candidate/interview/${data.videoProfileId}`);
      })
      .catch((error) => {
        console.error("API Error:", error);

        // Enhanced error logging for debugging 400 errors
        fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(apiPayload),
        })
          .then(async (response) => {
            const data = await response.json().catch(() => ({}));
            if (!response.ok) {
              console.error("API Response Body:", data);
              toast.error(data.message || "API Error: " + response.status);
              throw new Error(`HTTP error! status: ${response.status} | ${JSON.stringify(data)}`);
            }
            return data;
          })
          .then((data) => {
            console.log("API Response:", data);
            console.log("Questions from API:", data.questions);

            if (!data.questions || !Array.isArray(data.questions) || data.questions.length === 0) {
              throw new Error("No questions received from API");
            }

            localStorage.setItem("formData", JSON.stringify(profileData));
            saveqnscookie(data);

            navigate(`/candidate/interview/${data.videoProfileId}`);
          })
          .catch((error) => {
            console.error("API Error (enhanced):", error);

            let errorMessage = "Failed to generate interview questions. ";

            if (error.message === "No questions received from API") {
              errorMessage += "Please try again or contact support if the issue persists.";
            } else if (error.response?.data?.message) {
              errorMessage += error.response.data.message;
            } else {
              errorMessage += error.message || "An unexpected error occurred.";
            }

            toast.error(errorMessage);
            setIsLoading(false);
          })
          .finally(() => {
            setIsLoading(false);
          });

      })
      .finally(() => {
        setIsLoading(false); // Hide the loader
      });
    // Form validation only - actual submission happens in TermsAndConditionsSection
    console.log("Form validation completed:", formData);
  };

  // Handle resume selection
  const handleResumeSelection = (e) => {
    const selectedId = e.target.value;
    setSelectedResumeId(selectedId);

    if (selectedId === "new") {
      // User wants to create a new resume, proceed to step 1
      setStep(1);
    } else if (selectedId === "default") {
      // User selected the default uploaded resume
      if (completeResumeData) {
        // Extract relevant data from the complete resume for video profile creation using enhanced extraction
        const primarySkills = extractSkillsFromResumeData(completeResumeData);
        const secondarySkills = extractSecondarySkillsFromResumeData(completeResumeData);
        const experience = completeResumeData.experience || [];
        const latestJob = experience.length > 0 ? experience[0] : null;

        // Set secondary skills for suggestions
        setSecondarySkillSuggestions(secondarySkills);

        setFormData({
          candId: candId,
          jobRole: latestJob?.title || "",
          skills: primarySkills, // Use AI-selected top 5 skills
          companyType: "", // Will be filled in next steps
          experience: "", // Will be filled in next steps
          salary: { current: "", expected: "" }, // Will be filled in next steps
        });

        console.log('Enhanced skills extraction:', {
          primary_skills: primarySkills,
          secondary_skills: secondarySkills,
          total_available: primarySkills.length + secondarySkills.length
        });

        // Proceed to step 1 to fill in missing details
        setStep(1);
      }
    } else if (selectedId) {
      // User selected an existing video resume, pre-populate form data
      const selectedResume = existingResumes.find(resume => resume.id.toString() === selectedId);
      if (selectedResume) {
        setFormData({
          candId: candId,
          jobRole: selectedResume.role,
          skills: selectedResume.skills ? selectedResume.skills.split(", ") : [],
          companyType: selectedResume.job_type || "",
          experience: selectedResume.experience_range || "",
          salary: selectedResume.salary ? JSON.parse(selectedResume.salary) : { current: "", expected: "" },
        });
        // Skip to final step for confirmation
        setStep(4);
      }
    }
    setError("");
  };

  const handleNextStep = (e) => {
    e.preventDefault();
    // Validate based on current step
    if (step === 0) {
      if (!selectedResumeId) {
        setError("Please select a resume or choose to create a new one.");
        return;
      }
      // Handle the selection based on the selected option
      if (selectedResumeId === "new") {
        setStep(1);
      } else if (selectedResumeId === "default") {
        // Use the default resume data
        handleResumeSelection({ target: { value: "default" } });
      } else {
        // Use existing video resume
        handleResumeSelection({ target: { value: selectedResumeId } });
      }
      return;
    } else if (step === 1) {
      if (!jobRoleInput || jobRoleInput.trim() === "" || formData.skills.length === 0) {
        setError("Please fill the required Job Role and add at least one skill.");
        // Focus the Job Role input if empty
        if (!jobRoleInput || jobRoleInput.trim() === "") {
          const jobRoleInputEl = document.getElementById("jobRole");
          if (jobRoleInputEl) jobRoleInputEl.focus();
        }
        return;
      }
    } else if (step === 2) {
      if (!formData.companyType) {
        setError("Please select a company type.");
        return;
      }
    } else if (step === 3) {
      if (!formData.experience) {
        setError("Please select your experience level.");
        return;
      }
      if (
        (formData.experience === "2-3" || formData.experience === "3-5") &&
        (!formData.salary.current || !formData.salary.expected)
      ) {
        setError("Please enter both current and expected salary.");
        return;
      }
    }
    setError(""); // Clear the error if validation passes
    if (step < 4) {
      setStep(step + 1);
    }
  };

  const handlePrevStep = () => {
    if (step > 0) {
      setStep(step - 1);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "currentSalary") {
      // Handling the nested salary.current
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          current: value, // Update only salary.current
        },
      }));
    } else if (name === "expectedSalary") {
      // Handling the nested salary.expected
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          expected: value, // Update only salary.expected
        },
      }));
    } else {
      // Handling other fields
      setFormData({ ...formData, [name]: value });
    }
  };

  const salaryOptions = [
    { value: "0-5", label: "0-5 LPA" },
    { value: "5-10", label: "5-10 LPA" },
    { value: "10-15", label: "10-15 LPA" },
    { value: "15-20", label: "15-20 LPA" },
    { value: "20+", label: "20+ LPA" },
  ];

  // Handler for uploading new resume
  const handleNewResumeUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setIsUploadingResume(true);
    try {
      const formData = new FormData();
      formData.append('resume', file);
      formData.append('cand_id', candId);
      // Use the original backend endpoint for uploading resume
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/upload-resume`, {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to upload resume');
      toast.success('Resume uploaded and replaced successfully!');
      // Refresh resume data
      await fetchResumeData();
      setSelectedResumeId('default');
    } catch (err) {
      toast.error('Failed to upload new resume');
      console.error(err);
    } finally {
      setIsUploadingResume(false);
    }
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm p-2 overflow-y-auto">
      <div className="w-full max-w-3xl my-4">
        <div
          ref={formRef}
          className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-2xl overflow-hidden max-h-[95vh] flex flex-col"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-3 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-white/20 rounded-lg">
                  <Video className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-white">
                    Create Video Resume
                  </h2>
                  <p className="text-blue-100 text-xs">
                    Step {step + 1} of 5
                  </p>
                </div>
              </div>
              <button
                onClick={togglePopupVR}
                className="p-1.5 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all duration-200"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Progress</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">{Math.round((step / 4) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
              <div
                className="bg-gradient-to-r from-blue-600 to-purple-600 h-1.5 rounded-full transition-all duration-500"
                style={{ width: `${(step / 4) * 100}%` }}
              ></div>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <form
              id="multistepForm"
              className="p-4"
              onSubmit={handleFinalSubmit}
            >
              {/* Error Message */}
              {error && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <X className="w-4 h-4 text-red-500" />
                    <p className="text-red-700 dark:text-red-400 text-sm font-medium">{error}</p>
                  </div>
                </div>
              )}

              {/* Step 0 - Resume Selection */}
              {step === 0 && (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                      <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                      Select Your Resume
                    </h1>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Choose an existing resume or create a new one
                    </p>
                  </div>

                  {isLoadingResumes ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-3 border-blue-600 border-t-transparent"></div>
                      <span className="mt-3 text-gray-600 dark:text-gray-400 text-sm">Loading your resumes...</span>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {/* Resume Options */}
                      <div className="grid gap-3">
                        {/* Default Resume Option */}
                        {resumeData && (
                          <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                            selectedResumeId === "default"
                              ? "border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md"
                              : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 bg-white dark:bg-gray-800"
                          }`}>
                            <input
                              type="radio"
                              name="resumeSelect"
                              value="default"
                              checked={selectedResumeId === "default"}
                              onChange={handleResumeSelection}
                              className="sr-only"
                            />
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg">
                                <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm">My Uploaded Resume</h3>
                                  <span className="inline-flex items-center rounded-full bg-gradient-to-r from-green-500 to-emerald-500 px-2 py-0.5 text-xs font-medium text-white">
                                    Recommended
                                  </span>
                                </div>
                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                                  Use your previously uploaded resume data
                                </p>
                                {selectedResumeId === "default" && (
                                  <div className="mt-2">
                                    <button
                                      type="button"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setShowResumePreview(!showResumePreview);
                                      }}
                                      className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-xs font-medium transition-colors"
                                    >
                                      <HiEye className="w-3 h-3" />
                                      {showResumePreview ? 'Hide Preview' : 'View Resume'}
                                    </button>
                                  </div>
                                )}
                              </div>
                              {selectedResumeId === "default" && (
                                <CheckCircle className="w-5 h-5 text-green-500" />
                              )}
                            </div>
                          </label>
                        )}

                       
                        {/* Upload New Resume Option */}
                        <label
                          htmlFor="new-resume-upload-trigger"
                          className="relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 bg-white dark:bg-gray-800 hover:shadow-md"
                        >
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg">
                              <Upload className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">Upload New Resume</h3>
                              <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                                Click to upload a new resume file and create your video profile
                              </p>
                              {isUploadingResume && (
                                <div className="mt-2 flex items-center gap-2">
                                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-purple-600 border-t-transparent"></div>
                                  <span className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                                    Uploading your resume...
                                  </span>
                                </div>
                              )}
                            </div>
                            <Upload className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                          </div>
                          <input
                            id="new-resume-upload-trigger"
                            type="file"
                            accept=".pdf,.doc,.docx"
                            className="hidden"
                            onChange={(e) => {
                              handleNewResumeUpload(e);
                              // After upload, we can show preview option
                            }}
                            disabled={isUploadingResume}
                          />
                        </label>
                      </div>

                      {/* No resumes message */}
                      {existingResumes.length === 0 && !resumeData && (
                        <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                          <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            No existing resumes found. Create a new one to get started.
                          </p>
                        </div>
                      )}

                      {/* Success message for default resume */}
                      {selectedResumeId === "default" && (
                        <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <p className="text-green-800 dark:text-green-200 text-sm font-medium">
                              Perfect! Using your uploaded resume data
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Resume Preview - Only shown when preview is toggled */}
                      {resumeData && showResumePreview && (
                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 max-h-40 overflow-y-auto">
                          <ResumePreview resumeData={resumeData} isCompact={true} />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

            {/* Step 1 - Job Role and Skills */}
            {step === 1 && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                    <HiBriefcase className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                    Job Role & Skills
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tell us about your target role and expertise
                  </p>
                </div>

                <div className="grid gap-4">
                  {/* Job Role Input */}
                  <div className="relative">
                    <label htmlFor="jobRole" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Job Role *
                    </label>
                    <input
                      required
                      id="jobRole"
                      name="jobRole"
                      type="text"
                      value={jobRoleInput}
                      onChange={handleJobRoleInput}
                      onFocus={() => setShowJobRoleSuggestions(true)}
                      onBlur={handleJobRoleBlur}
                      placeholder="e.g., Frontend Developer, Data Scientist"
                      className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                    />
                    {showJobRoleSuggestions && jobRoleSuggestions.length > 0 && (
                      <ul
                        ref={jobRef}
                        className="absolute z-10 mt-1 max-h-32 w-full overflow-y-auto rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-xl"
                      >
                        {jobRoleSuggestions.map((suggestion, index) => (
                          <li
                            key={index}
                            className="cursor-pointer px-3 py-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-gray-900 dark:text-white transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0 text-sm"
                            onClick={() => handleJobRoleSuggestionClick(suggestion)}
                          >
                            {suggestion}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>

                  {/* Enhanced Skills Section */}
                  <div className="relative">
                    <div className="flex items-center justify-between mb-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Skills * ({formData.skills.length} selected)
                        {(completeResumeData?.skills?.primary_skills || hasAutoSelected) && (
                          <span className="text-xs text-blue-600 ml-2">
                            (AI-suggested skills included)
                          </span>
                        )}
                      </label>
                      {formData.skills.length > 0 && (
                        <button
                          type="button"
                          onClick={clearAllSkills}
                          className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                          title="Clear all skills"
                        >
                          <X size={12} />
                          Clear All
                        </button>
                      )}
                    </div>

                    <input
                      type="text"
                      id="skill-input"
                      value={skillInput}
                      onChange={handleSkillInput}
                      onFocus={() => setShowSkillSuggestions(true)}
                      placeholder="Type to search skills..."
                      className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (skillInput.trim()) {
                            addSkill(skillInput.trim());
                            setSkillInput("");
                          }
                        }
                      }}
                    />
                    {/* Skills suggestions from database */}
                    {showSkillSuggestions && skillSuggestions.length > 0 && (
                      <div className="absolute z-10 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md mt-1 max-h-40 overflow-y-auto">
                        {skillSuggestions.map((suggestion, index) => (
                          <div
                            key={index}
                            className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => addSkill(suggestion)}
                          >
                            {suggestion}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Selected Skills Display */}
                    {formData.skills.length > 0 && (
                      <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Selected Skills ({formData.skills.length})
                        </p>
                        <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
                          {formData.skills.map((skill, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full text-xs font-medium"
                            >
                              {skill}
                              <button
                                type="button"
                                className="hover:bg-white/20 rounded-full p-0.5 transition-colors"
                                onClick={() => removeSkill(skill)}
                              >
                                <X size={12} />
                              </button>
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Enhanced Help text */}
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {formData.skills.length === 0 && "Add your relevant skills"}
                      {formData.skills.length > 0 && `${formData.skills.length} skill${formData.skills.length === 1 ? '' : 's'} selected`}
                    </p>

                    {formData.skills.length === 0 && (
                      <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <p className="text-xs text-yellow-800 dark:text-yellow-200">
                          💡 Add at least one skill to continue
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 2 - Company Type */}
            {step === 2 && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/50 dark:to-teal-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                    <HiOfficeBuilding className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                    Company Preference
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    What type of company environment do you prefer?
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {/* MNC Option */}
                  <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    formData.companyType === "mnc"
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 bg-white dark:bg-gray-800 hover:shadow-md"
                  }`}>
                    <input
                      type="radio"
                      name="companyType"
                      value="mnc"
                      checked={formData.companyType === "mnc"}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                        formData.companyType === "mnc"
                          ? "bg-blue-100 dark:bg-blue-900/50"
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <HiOfficeBuilding className={`w-5 h-5 ${
                          formData.companyType === "mnc" ? "text-blue-600 dark:text-blue-400" : "text-gray-500"
                        }`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">MNC</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Multinational Corporation</p>
                    </div>
                    {formData.companyType === "mnc" && (
                      <div className="absolute top-2 right-2">
                        <CheckCircle className="w-4 h-4 text-blue-500" />
                      </div>
                    )}
                  </label>

                  {/* Mid Range Option */}
                  <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    formData.companyType === "mid_range"
                      ? "border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600 bg-white dark:bg-gray-800 hover:shadow-md"
                  }`}>
                    <input
                      type="radio"
                      name="companyType"
                      value="mid_range"
                      checked={formData.companyType === "mid_range"}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                        formData.companyType === "mid_range"
                          ? "bg-green-100 dark:bg-green-900/50"
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <HiUserGroup className={`w-5 h-5 ${
                          formData.companyType === "mid_range" ? "text-green-600 dark:text-green-400" : "text-gray-500"
                        }`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">Mid-Size</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Growing Company</p>
                    </div>
                    {formData.companyType === "mid_range" && (
                      <div className="absolute top-2 right-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      </div>
                    )}
                  </label>

                  {/* Startup Option */}
                  <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    formData.companyType === "startup"
                      ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 bg-white dark:bg-gray-800 hover:shadow-md"
                  }`}>
                    <input
                      type="radio"
                      name="companyType"
                      value="startup"
                      checked={formData.companyType === "startup"}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                        formData.companyType === "startup"
                          ? "bg-purple-100 dark:bg-purple-900/50"
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <HiLightBulb className={`w-5 h-5 ${
                          formData.companyType === "startup" ? "text-purple-600 dark:text-purple-400" : "text-gray-500"
                        }`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">Startup</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Early-stage Company</p>
                    </div>
                    {formData.companyType === "startup" && (
                      <div className="absolute top-2 right-2">
                        <CheckCircle className="w-4 h-4 text-purple-500" />
                      </div>
                    )}
                  </label>
                </div>
              </div>
            )}

            {/* Step 3 - Experience Level */}
            {step === 3 && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-100 to-orange-100 dark:from-amber-900/50 dark:to-orange-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                    <HiStar className="w-6 h-6 text-amber-600 dark:text-amber-400" />
                  </div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                    Experience Level
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Select your professional experience level
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {/* Fresher */}
                  <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    formData.experience === "0-1"
                      ? "border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600 bg-white dark:bg-gray-800 hover:shadow-md"
                  }`}>
                    <input
                      type="radio"
                      name="experience"
                      value="0-1"
                      checked={formData.experience === "0-1"}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                        formData.experience === "0-1"
                          ? "bg-green-100 dark:bg-green-900/50"
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <HiAcademicCap className={`w-5 h-5 ${
                          formData.experience === "0-1" ? "text-green-600 dark:text-green-400" : "text-gray-500"
                        }`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">Fresher</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">0-1 years</p>
                    </div>
                    {formData.experience === "0-1" && (
                      <div className="absolute top-2 right-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      </div>
                    )}
                  </label>

                  {/* Intermediate */}
                  <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    formData.experience === "2-3"
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 bg-white dark:bg-gray-800 hover:shadow-md"
                  }`}>
                    <input
                      type="radio"
                      name="experience"
                      value="2-3"
                      checked={formData.experience === "2-3"}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                        formData.experience === "2-3"
                          ? "bg-blue-100 dark:bg-blue-900/50"
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <HiBriefcase className={`w-5 h-5 ${
                          formData.experience === "2-3" ? "text-blue-600 dark:text-blue-400" : "text-gray-500"
                        }`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">Intermediate</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">2-3 years</p>
                    </div>
                    {formData.experience === "2-3" && (
                      <div className="absolute top-2 right-2">
                        <CheckCircle className="w-4 h-4 text-blue-500" />
                      </div>
                    )}
                  </label>

                  {/* Experienced */}
                  <label className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    formData.experience === "3-5"
                      ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 bg-white dark:bg-gray-800 hover:shadow-md"
                  }`}>
                    <input
                      type="radio"
                      name="experience"
                      value="3-5"
                      checked={formData.experience === "3-5"}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                        formData.experience === "3-5"
                          ? "bg-purple-100 dark:bg-purple-900/50"
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <HiStar className={`w-5 h-5 ${
                          formData.experience === "3-5" ? "text-purple-600 dark:text-purple-400" : "text-gray-500"
                        }`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">Experienced</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">3-5 years</p>
                    </div>
                    {formData.experience === "3-5" && (
                      <div className="absolute top-2 right-2">
                        <CheckCircle className="w-4 h-4 text-purple-500" />
                      </div>
                    )}
                  </label>
                </div>

                {/* Conditional Salary Fields */}
                {(formData.experience === "2-3" || formData.experience === "3-5") && (
                  <div className="mt-4 space-y-3">
                    {/* Current Salary */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Salary</label>
                      <select
                        value={formData.salary.current}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            salary: {
                              current: e.target.value,
                              expected: formData.salary.expected,
                            },
                          })
                        }
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="">Select current salary</option>
                        {salaryOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Expected Salary */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expected Salary</label>
                      <select
                        value={formData.salary.expected}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            salary: {
                              current: formData.salary.current,
                              expected: e.target.value,
                            },
                          })
                        }
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="">Select expected salary</option>
                        {salaryOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 4 - Terms and Conditions */}
            {step === 4 && (
              <div>
                <TermsAndConditionsSection
                  formData={formData}
                  isLoading={isLoading}
                  handleSubmit={handleFinalSubmit}
                />
              </div>
            )}

              {/* Navigation Buttons for steps 1-3 */}
              {step < 4 && (
                <div className="mt-4 flex justify-end gap-2">
                  {step > 0 && (
                    <button
                      type="button"
                      id="prevBtn"
                      className="rounded-lg text-blue-600 bg-white border border-blue-200 px-3 py-2 text-sm font-medium transition-all duration-200 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onClick={handlePrevStep}
                    >
                      Previous
                    </button>
                  )}
                  <button
                    type="button"
                    id="nextBtn"
                    className="rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 text-sm font-medium text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={handleNextStep}
                  >
                    {step === 0 ? (selectedResumeId === "default" ? "Continue with My Resume" : selectedResumeId ? "Continue" : "Select Resume") : "Next"}
                  </button>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateVR;