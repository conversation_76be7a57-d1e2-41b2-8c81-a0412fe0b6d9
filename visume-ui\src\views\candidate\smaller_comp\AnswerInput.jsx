import React, { useState, useEffect } from "react";
import Editor from "@monaco-editor/react";
import { Clock, Mic, ArrowRight, LogOut, Code, MessageSquare, Loader2, Play } from "lucide-react";

export default function AnswerInput({
  question,
  isListening,
  handleSpeechRecognition,
  handleNextQuestion,
  onEndInterview,
  currentIndex, 
  isSpeaking,
  remainingTime,
  isProcessing,
  startAnswering,
  updateAnswer, 
  isInterviewActive = false, 
}) {
  const [localAnswer, setLocalAnswer] = useState(question?.answer || "");

  // Update local answer when question changes (e.g., next question loads)
  useEffect(() => {
    setLocalAnswer(question?.answer || "");
  }, [question]);

  // Function to get question type information for styling
  const getQuestionTypeInfo = () => {
    const type = (question?.type || "").toLowerCase();

    switch (type) {
      case "coding":
      case "technical":
        return {
          label: "Technical Question",
          icon: <Code className="w-5 h-5" />,
          color: "from-purple-500 to-indigo-500",
          bgColor: "from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20",
          borderColor: "border-purple-200 dark:border-purple-800"
        };
      case "behavioral":
      case "verbal":
        return {
          label: "Behavioral Question",
          icon: <MessageSquare className="w-5 h-5" />,
          color: "from-green-500 to-emerald-500",
          bgColor: "from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20",
          borderColor: "border-green-200 dark:border-green-800"
        };
      default:
        return {
          label: "Interview Question",
          icon: <MessageSquare className="w-5 h-5" />,
          color: "from-blue-500 to-cyan-500",
          bgColor: "from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20",
          borderColor: "border-blue-200 dark:border-blue-800"
        };
    }
  };

  const handleCodeMirrorChange = (value) => {
    if (!isInterviewActive) {
      return;
    }
    if (value && !question?.startTimestamp) {
      startAnswering();
    }
    setLocalAnswer(value);
  };

  const handleNextButtonClick = () => {
    // For technical/other questions, pass the local answer to the parent
    if ((question?.type || "").toLowerCase() === "coding" || (question?.type || "").toLowerCase() === "other") {
      handleNextQuestion(localAnswer);
    } else {
      // For behavioral questions, the handleNextQuestion will manage its own audio transcription
      handleNextQuestion();
    }
  };

  const typeInfo = getQuestionTypeInfo();

  return (
    <div className={`bg-white dark:bg-gray-900 border ${typeInfo.borderColor} rounded-xl shadow-sm overflow-hidden flex flex-col h-full max-h-screen`}>
      {/* Header */}
      <div className={`bg-gradient-to-r ${typeInfo.bgColor} border-b ${typeInfo.borderColor} px-3 py-2 sm:py-3 flex-shrink-0`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className={`p-1.5 sm:p-2 bg-gradient-to-r ${typeInfo.color} rounded-lg`}>
              <div className="text-white">
                {typeInfo.icon}
              </div>
            </div>
            <div>
              <h3 className="text-sm sm:text-md font-semibold text-gray-900 dark:text-white">
                {typeInfo.label}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {question?.type === "behavioral" ? "Share your experience and thoughts" :
                 question?.type === "technical" ? "Write your code solution below" :
                 "Provide your detailed response"}
              </p>
            </div>
          </div>

          {/* Timer */}
          {!isSpeaking && remainingTime > 0 && (
            <div className="flex items-center gap-1 sm:gap-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1 sm:py-2">
              <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-red-500" />
              <span className="text-sm sm:text-lg font-bold text-red-600 dark:text-red-400">
                {remainingTime}s
              </span>
            </div>
          )}
        </div>
      </div>
      {/* Content Area - Responsive with proper height management */}
      <div className="flex-1 p-3 sm:p-4 lg:p-6 overflow-hidden">
        {(question?.type || "").toLowerCase() === "behavioral" || (question?.type || "").toLowerCase() === "verbal" ? (
          <div className="h-full flex flex-col items-center justify-center">
            {/* Show auto-recording message when AI is speaking */}
            {isSpeaking && (
              <div className="text-center space-y-3 sm:space-y-4">
                <div className="mx-auto w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-full flex items-center justify-center">
                  <Play className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-base sm:text-lg font-medium text-gray-900 dark:text-white mb-1 sm:mb-2">
                    AI is narrating the question
                  </p>
                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                    Recording will start automatically when narration ends
                  </p>
                </div>
              </div>
            )}

            {!isSpeaking && !isListening && (
              <div className="text-center space-y-3 sm:space-y-4">
                <div className="mx-auto w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 rounded-full flex items-center justify-center">
                  <Mic className="w-6 h-6 sm:w-8 sm:h-8 text-green-600 dark:text-green-400" />
                </div>
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 sm:py-2.5 px-4 sm:px-6 rounded-md shadow-sm transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base min-h-[44px] touch-manipulation"
                  onClick={() => {
                    if (isInterviewActive) {
                      startAnswering();
                      handleSpeechRecognition();
                    }
                  }}
                  disabled={!isInterviewActive}
                >
                  <div className="flex items-center justify-center gap-1.5">
                    <Mic className="w-4 h-4" />
                    <span>Start Recording</span>
                  </div>
                </button>
              </div>
            )}

            {isListening && (
              <div className="text-center space-y-3 sm:space-y-4">
                <div className="mx-auto w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-red-100 to-pink-100 dark:from-red-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center">
                  <Mic className="w-8 h-8 sm:w-10 sm:h-10 text-red-500 animate-pulse" />
                </div>
                <div>
                  <p className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-1 sm:mb-2">
                    Listening...
                  </p>
                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                    Speak clearly and take your time
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (question?.type || "").toLowerCase() === "coding" || (question?.type || "").toLowerCase() === "technical" || (question?.type || "").toLowerCase() === "other" ? (
          <div className="h-full flex flex-col min-h-0">
            <div className="flex-1 rounded-lg overflow-hidden border border-gray-700 p-1 sm:p-2 min-h-[200px] sm:min-h-[250px] lg:min-h-[300px] max-h-[60vh] sm:max-h-[50vh] lg:max-h-none code-editor-mobile" style={{ backgroundColor: '#0d1117' }}>
              <Editor
                height="100%"
                language="javascript"
                theme="vs-dark"
                value={localAnswer}
                onChange={handleCodeMirrorChange}
                options={{
                  readOnly: !isInterviewActive,
                  lineNumbers: 'on',
                  folding: true,
                  minimap: { enabled: false },
                  wordWrap: 'on',
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                  fontSize: typeof window !== 'undefined' && window.innerWidth < 640 ? 12 : 14,
                }}
              />
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-full flex items-center justify-center">
                <MessageSquare className="w-8 h-8 text-gray-500" />
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                Unsupported question type: {question?.type}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Footer Actions - Sticky positioning for better accessibility */}
      <div className="border-t border-gray-200 dark:border-gray-800 px-3 sm:px-4 lg:px-6 py-3 sm:py-4 bg-white dark:bg-gray-900 flex-shrink-0 answer-input-footer">
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-2 sm:gap-4">
          <button
            onClick={handleNextButtonClick}
            disabled={isProcessing || isSpeaking || !isInterviewActive}
            className={`flex items-center justify-center gap-1.5 px-3 sm:px-4 py-2 sm:py-2.5 rounded-md font-medium transition-colors duration-200 text-sm ${
              isProcessing || isSpeaking || !isInterviewActive
                ? "bg-gray-100 dark:bg-gray-800 text-gray-400 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
            }`}
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <span>Next Question</span>
                <ArrowRight className="w-3.5 h-3.5" />
              </>
            )}
          </button>

          <button
            onClick={(e) => {
              e.preventDefault();
              console.log("🏁 Finish Interview button clicked", {
                currentIndex,
                questionsCompleted: currentIndex + 1,
                isEnabled: currentIndex + 1 >= 5 && isInterviewActive,
                isInterviewActive
              });
              if (currentIndex + 1 >= 5) {
                // Save the current answer before ending the interview
                if (
                  (question?.type || "").toLowerCase() === "coding" ||
                  (question?.type || "").toLowerCase() === "technical" ||
                  (question?.type || "").toLowerCase() === "other"
                ) {
                  handleNextQuestion(localAnswer);
                }
                onEndInterview();
              }
            }}
            disabled={currentIndex + 1 < 5 || !isInterviewActive || isProcessing || isSpeaking}
            className={`flex items-center justify-center gap-1.5 px-3 sm:px-4 py-2 sm:py-2.5 rounded-md font-medium transition-colors duration-200 text-sm ${
              currentIndex + 1 < 5 || !isInterviewActive || isProcessing || isSpeaking
                ? "bg-gray-100 dark:bg-gray-800 text-gray-400 cursor-not-allowed"
                : "bg-green-600 hover:bg-green-700 text-white shadow-sm"
            }`}
            title={
              currentIndex + 1 < 5
                ? "Please complete at least 5 questions"
                : "Click to finish the interview"
            }
          >
            <span className="text-center">
              Finish Interview{" "}
              {currentIndex + 1 < 5
                ? `(${5 - (currentIndex + 1)} more required)`
                : "✓"}
            </span>
            <LogOut className="w-3.5 h-3.5" />
          </button>
        </div>
      </div>
    </div>
  );
}
