import Cookies from "js-cookie";
import { useState, useEffect } from "react";
import { useRef } from "react";
import { Input } from "@material-tailwind/react";
import { Link } from "react-router-dom";
import toast from "react-hot-toast";
// import { Select, Option } from "@material-tailwind/react";

export default function JSRegister() {
  const jsregcookie = (data) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    localStorage.clear();
    Cookies.set("candId", data.cand_id, { expires: 7 });
    Cookies.set("jstoken", data.token, { expires: 7 });
    Cookies.set("role", data.role, { expires: 7 });
  };
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    phone: "",
    gender: "",
    marks10th: "",
    marks12th: "",
    marksDegree: "",
    skills: [],
    languages: [],
    profilePicture: null,
    resume: null,
    preferredLocation: "",
  });
  const [skillInput, setSkillInput] = useState("");
  const [skillSuggestions, setSkillSuggestions] = useState([
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "CSS",
    "HTML",
    "Tailwind CSS",
    "Django",
  ]);

  const [langInput, setLangInput] = useState("");
  const [selectedLang, setSelectedLang] = useState([]);
  const [filteredLangSuggestions, setFilteredLangSuggestions] = useState([]);
  const [showLangSuggestions, setShowLangSuggestions] = useState(false);
  const langsuggestionsRef = useRef(null);

  const langSuggestions = [
    "English",
    "Hindi",
    "Kannada",
    "Spanish",
    "Tamil",
    "Telugu",
    "Malayalam",
    "Marathi",
  ];

  const [errors, setErrors] = useState({});
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState(["", "", "", ""]);

  const [generatedOtp, setGeneratedOtp] = useState("");

  const steps = [
    { title: "Set up Account", fields: ["name", "email", "password"] },
    { title: "Set up Phone", fields: ["phone"] },
    {
      title: "More about you",
      fields: ["gender", "marks10th", "marks12th", "marksDegree"],
    },
    { title: "Your Skills", fields: ["skills", "languages"] },
    {
      title: "Confirm Your Details",
      fields: ["profilePicture", "resume", "preferredLocation"],
    },
  ];

  const handleInputChange = (e) => {
    const { name, value, type, files } = e.target;

    if (type === "file") {
      setFormData((prev) => ({
        ...prev,
        [name]: files[0],
      }));
    } else if (name === "phone") {
      const numericValue = value.replace(/[^0-9]/g, "").slice(0, 10);
      if (value !== numericValue) {
        toast.error("Please enter only numbers.");
      }
      setFormData((prev) => ({ ...prev, [name]: numericValue }));
    } else if (name === "skills") {
      setSkillInput(value);
      updateSuggestions(value);
      setShowSuggestions(true);
    } else if (name === "languages") {
      const inputValue = e.target.value.toLowerCase();
      setLangInput(inputValue);

      const suggestions = langSuggestions.filter(
        (lang) =>
          lang.toLowerCase().includes(inputValue) &&
          !selectedLang.includes(lang)
      );
      setFilteredLangSuggestions(suggestions);
      setShowLangSuggestions(true);
    } else {
      // Handle other fields normally
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const validateStep = () => {
    const currentFields = steps[currentStep].fields;
    const newErrors = {};

    currentFields.forEach((field) => {
      if (!formData[field]) {
        newErrors[field] = `${field} is required`;
      }
    });

    if (currentStep === 0) {
      if (
        formData.email &&
        !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
      ) {
        newErrors.email = "Invalid email format";
      }
    }

    if (currentStep === 1 && !phoneVerified) {
      newErrors.phone = "Phone number must be verified";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = (e) => {
    e.preventDefault(); // Prevent form submission
    if (validateStep()) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const generateOtp = () => {
    return Math.floor(1000 + Math.random() * 9000).toString();
  };

  const sendOtp = () => {
    const otp = generateOtp();
    setGeneratedOtp(otp);
    setOtpSent(true);
    toast.success(`OTP sent: ${otp}`); // For testing, you can remove this line in production
  };

  const verifyOtp = () => {
    const enteredOtp = otp.join("");
    if (enteredOtp === generatedOtp) {
      setPhoneVerified(true);
      toast.success("Phone verified!");
    } else {
      toast.error("Invalid OTP. Please try again.");
    }
  };
  useEffect(() => {
    const progressBar = document.getElementById("progressBar");
    if (progressBar) {
      const progressWidth = ((currentStep + 1) / steps.length) * 100;
      progressBar.style.width = `${progressWidth}%`;
    }
  }, [currentStep]);

  // Skills

  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const skillsuggestionsRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        skillsuggestionsRef.current &&
        !skillsuggestionsRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const updateSuggestions = (inputValue) => {
    const filtered = skillSuggestions.filter(
      (skill) =>
        skill.toLowerCase().includes(inputValue.toLowerCase()) &&
        !selectedSkills.includes(skill)
    );
    setFilteredSuggestions(filtered);
  };

  const addSkill = (skill) => {
    if (!selectedSkills.includes(skill) && skillSuggestions.includes(skill)) {
      setSelectedSkills((prev) => [...prev, skill]);
      setFormData((prev) => ({
        ...prev,
        skills: [...prev.skills, skill], // Update formData.skills
      }));
      setSkillInput("");
      setShowSuggestions(false);
    }
  };

  const removeSkill = (skill) => {
    setSelectedSkills((prev) => prev.filter((s) => s !== skill));
    setFormData((prev) => ({
      ...prev,
      skills: prev.skills.filter((s) => s !== skill), // Update formData.skills
    }));
  };

  const handleKeyPressskill = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      const inputValue = skillInput.trim();
      if (
        skillSuggestions.includes(inputValue) &&
        !selectedSkills.includes(inputValue)
      ) {
        addSkill(inputValue);
      }
    }
  };

  const addLang = (lang) => {
    if (!selectedLang.includes(lang)) {
      setSelectedLang([...selectedLang, lang]);
      setFormData((prev) => ({
        ...prev,
        languages: [...prev.languages, lang], // Update formData.languages
      }));
      setLangInput("");
      setShowLangSuggestions(false);
    }
  };

  const removeLang = (langToRemove) => {
    setSelectedLang(selectedLang.filter((lang) => lang !== langToRemove));
    setFormData((prev) => ({
      ...prev,
      languages: prev.languages.filter((lang) => lang !== langToRemove), // Update formData.languages
    }));
  };

  const handleKeyPresslang = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (
        langSuggestions.includes(langInput) &&
        !selectedLang.includes(langInput)
      ) {
        addLang(langInput);
      }
    }
  };

  const handleFocus = () => {
    const suggestions = langSuggestions.filter(
      (lang) => !selectedLang.includes(lang)
    );
    setFilteredLangSuggestions(suggestions);
    setShowLangSuggestions(true);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        langsuggestionsRef.current &&
        !langsuggestionsRef.current.contains(event.target)
      ) {
        setShowLangSuggestions(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateStep()) {
      // Here you would typically send the data to your server

      const finalFormData = {
        email: formData.email,
        password: formData.password,
        cand_name: formData.name,
        cand_mobile: formData.phone,
        marks: {
          "10th": formData.marks10th,
          "12th": formData.marks12th,
          degree: formData.marksDegree,
        },
        gender: formData.gender,
        languages_known: formData.languages,
        cand_skills: formData.skills,
        preferred_location: formData.preferredLocation,
        resume: formData.resume ? formData.resume : null,
        profile_picture: formData.profilePicture
          ? formData.profilePicture
          : null,
      };
      console.log("Form submitted:", finalFormData);

      await registerJobseeker(finalFormData);
    }
  };

  // Define the registerJobseeker function
  async function registerJobseeker(formData) {
    // Create a new FormData object
    const formPayload = new FormData();

    // Append the fields to the FormData object
    formPayload.append("email", formData.email);
    formPayload.append("password", formData.password);
    formPayload.append("cand_name", formData.cand_name);
    formPayload.append("cand_mobile", formData.cand_mobile);
    formPayload.append("gender", formData.gender);
    formPayload.append("languages_known", formData.languages_known);
    formPayload.append("cand_skills", formData.cand_skills);
    formPayload.append("preferred_location", formData.preferred_location);

    // Append the marks object as a JSON string
    formPayload.append("marks", JSON.stringify(formData.marks));

    // Append files if they exist
    if (formData.resume) {
      formPayload.append("resume", formData.resume);
    }
    if (formData.profile_picture) {
      formPayload.append("profile_picture", formData.profile_picture);
    }

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/register-jobseeker`,
        {
          method: "POST",
          body: formPayload,
        }
      );

      if (response.ok) {
        const resdata = await response.json();
        console.log(resdata.message); // "Jobseeker registered successfully"
        console.log(resdata); // "Jobseeker registered successfully"
        jsregcookie(resdata); // Store the token, role, candId in a cookie
      } else if (response.status === 400) {
        const errorData = await response.json();
        console.error(errorData.message); // Handle missing file errors
      } else if (response.status === 409) {
        const errorData = await response.json();
        console.error(errorData.message); // "Email already exists"
      } else if (response.status === 500) {
        const errorData = await response.json();
        console.error(errorData.error); // Detailed error message
      } else {
        console.error("An unexpected error occurred");
      }
    } catch (error) {
      console.error("Network error:", error);
    }
    window.location.href = "/";
  }

  return (
    <div
      className="z-40 mx-auto mb-16 mt-2  flex h-full w-full max-w-2xl flex-col 
    items-center justify-center rounded-lg bg-white px-2 pt-10 shadow-md md:mx-0 md:px-0 lg:mb-10 lg:items-center lg:justify-start lg:p-8
    "
    >
      <div className="relative mb-8 h-2 rounded-full bg-gray-200">
        <div
          id="progressBar"
          className="absolute left-0 top-0 h-2 rounded-full bg-blue-600 transition-all duration-500"
          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
        />

        <div
          className="absolute left-0 top-0 h-2 rounded-full bg-blue-600 transition-all duration-500"
          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
        />
      </div>

      <form
        onSubmit={handleSubmit}
        enctype="multipart/form-data"
        className="w-full space-y-6"
      >
        <h1 className="mb-4 text-3xl font-semibold text-gray-800">
          {steps[currentStep].title}
        </h1>

        {currentStep === 0 && (
          <>
            <div>
              {/* <div className="w-72">
                <InputField
                  label="Email Address"
                  placeholder="@horizon.ui"
                  id="email"
                  type="text"
                />
              </div> */}
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700"
              >
                Name
              </label>
              <input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-500">{errors.name}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 "
              >
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-500">{errors.password}</p>
              )}
            </div>
          </>
        )}

        {currentStep === 1 && (
          <div>
            <label
              htmlFor="phone"
              className="block text-sm font-medium text-gray-700"
            >
              Phone
            </label>
            <div className="flex items-center justify-center space-x-2">
              <input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                maxLength="10"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              <button
                type="button"
                onClick={sendOtp}
                disabled={otpSent}
                className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Send OTP
              </button>
            </div>
            {errors.phone && (
              <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
            )}
            {otpSent && !phoneVerified && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">
                  Enter OTP
                </label>
                <div className="mt-1 flex space-x-2">
                  {[0, 1, 2, 3].map((index) => (
                    <input
                      key={index}
                      type="text"
                      maxLength={1}
                      value={otp[index]}
                      onChange={(e) => {
                        const newOtp = [...otp];
                        newOtp[index] = e.target.value;
                        setOtp(newOtp);
                      }}
                      className="w-12 rounded-md border-gray-300 text-center shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  ))}
                </div>
                <button
                  type="button"
                  onClick={verifyOtp}
                  className="mt-2 rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                >
                  Verify OTP
                </button>
              </div>
            )}
            {phoneVerified && (
              <p className="mt-2 text-green-500">Phone number verified!</p>
            )}
          </div>
        )}

        {currentStep === 2 && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Gender
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="male"
                  name="gender"
                  value="male"
                  checked={formData.gender === "male"}
                  onChange={() =>
                    setFormData((prev) => ({ ...prev, gender: "male" }))
                  }
                />
                <label
                  htmlFor="male"
                  className="text-sm font-medium text-gray-700"
                >
                  Male
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="female"
                  name="gender"
                  value="female"
                  checked={formData.gender === "female"}
                  onChange={() =>
                    setFormData((prev) => ({ ...prev, gender: "female" }))
                  }
                />
                <label
                  htmlFor="female"
                  className="text-sm font-medium text-gray-700"
                >
                  Female
                </label>
              </div>
              {errors.gender && (
                <p className="mt-1 text-sm text-red-500">{errors.gender}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="marks10th"
                className="block text-sm font-medium text-gray-700"
              >
                10th Marks (%)
              </label>
              <input
                id="marks10th"
                name="marks10th"
                type="number"
                min="1"
                max="100"
                value={formData.marks10th}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              {errors.marks10th && (
                <p className="mt-1 text-sm text-red-500">{errors.marks10th}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="marks12th"
                className="block text-sm font-medium text-gray-700"
              >
                12th Marks (%)
              </label>
              <input
                id="marks12th"
                name="marks12th"
                type="number"
                min="1"
                max="100"
                value={formData.marks12th}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              {errors.marks12th && (
                <p className="mt-1 text-sm text-red-500">{errors.marks12th}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="marksDegree"
                className="block text-sm font-medium text-gray-700"
              >
                Degree Marks (%)
              </label>
              <input
                id="marksDegree"
                name="marksDegree"
                type="number"
                min="1"
                max="100"
                value={formData.marksDegree}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
              {errors.marksDegree && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.marksDegree}
                </p>
              )}
            </div>
          </>
        )}

        {currentStep === 3 && (
          <>
            <h1>SKills here</h1>

            <input
              type="text"
              name="skills"
              value={skillInput}
              onChange={handleInputChange}
              onClick={handleInputChange}
              onKeyDown={handleKeyPressskill}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Enter a skill and press enter"
            />

            <span
              className={`text-red-400 ${
                selectedSkills.length ? "hidden" : "block"
              }`}
            >
              Select at least 1 skill
            </span>

            <ul
              ref={skillsuggestionsRef}
              className={`absolute z-10 max-h-[150px] overflow-auto rounded-md border border-gray-300 bg-white shadow-md ${
                showSuggestions ? "block" : "hidden"
              }`}
            >
              {filteredSuggestions.length > 0 ? (
                filteredSuggestions.map((skill, index) => (
                  <li
                    key={index}
                    className="cursor-pointer px-3 py-2 hover:bg-gray-200"
                    onClick={() => addSkill(skill)}
                  >
                    {skill}
                  </li>
                ))
              ) : (
                <li className="px-4 py-2 text-gray-500">No results found</li>
              )}
            </ul>

            <div className="mb-4 mt-3 h-auto max-h-[100px] w-full overflow-y-auto">
              <div className="flex flex-wrap gap-2">
                {selectedSkills.map((skill, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 rounded-full bg-gray-200 px-3 py-1 text-gray-700"
                  >
                    <span>{skill}</span>
                    <button
                      type="button"
                      className="text-red-500 hover:text-red-700"
                      onClick={() => removeSkill(skill)}
                    >
                      &times;
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <input
              type="text"
              name="languages"
              id="lang-input"
              value={langInput}
              onChange={handleInputChange}
              onKeyPress={handleKeyPresslang}
              onFocus={handleFocus}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Enter a language and press enter"
            />

            <span
              id="langerr"
              className={`text-red-400 ${
                selectedLang.length === 0 ? "" : "hidden"
              }`}
            >
              Select at least 1 language
            </span>

            {showLangSuggestions && (
              <ul
                ref={langsuggestionsRef}
                id="langsuggestionsul"
                className="absolute z-10 max-h-[150px] overflow-auto rounded-md border border-gray-300 bg-white shadow-md"
              >
                {filteredLangSuggestions.length > 0 ? (
                  filteredLangSuggestions.map((lang, index) => (
                    <li
                      key={index}
                      className="cursor-pointer px-3 py-2 hover:bg-gray-200"
                      onClick={() => addLang(lang)}
                    >
                      {lang}
                    </li>
                  ))
                ) : (
                  <li className="px-4 py-2 text-gray-500">No results found</li>
                )}
              </ul>
            )}

            <div className="mt-3 h-auto max-h-[100px] w-full overflow-y-auto">
              <div id="lang-container" className="flex flex-wrap gap-2">
                {selectedLang.map((lang, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 rounded-full bg-gray-200 px-3 py-1 text-gray-700"
                  >
                    <span>{lang}</span>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={() => removeLang(lang)}
                    >
                      &times;
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {currentStep === 4 && (
          <>
            {" "}
            <h1>upload docs here</h1>
            <div className="flex justify-around">
              <div className="mb-4">
                <label className="mb-2 block text-center font-medium text-gray-700">
                  Profile Picture
                </label>
                <label
                  id="profile-picture-label"
                  className="flex h-[150px] w-[150px] cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed border-blue-300 bg-gray-100 p-4 hover:bg-blue-50"
                >
                  <svg
                    id="profile-picture-icon"
                    className="mb-2 h-10 w-10 text-blue-500"
                    fill="currentColor"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 12c2.7 0 4.5-2.2 4.5-4.5S14.7 3 12 3 7.5 5.2 7.5 7.5 9.3 12 12 12zm0 2c-2.7 0-8 1.4-8 4.2V20h16v-1.8c0-2.8-5.3-4.2-8-4.2z" />
                  </svg>
                  <img
                    id="profile-picture-preview"
                    className="hidden h-[100px] w-[100px] rounded-md border object-cover"
                    alt="Profile Preview"
                  />
                  <span id="profile-picture-text" className="text-blue-500">
                    Select Photo
                  </span>
                  <input
                    name="profilePicture"
                    onChange={handleInputChange}
                    type="file"
                    id="profile-picture-input"
                    className="hidden"
                    accept="image/*"
                  />
                </label>
              </div>

              <div className="mb-4">
                <label className="mb-2 block text-center font-medium text-gray-700">
                  Resume (PDF or DOC)
                </label>
                <label
                  id="resume-label"
                  className="flex h-[150px] w-[150px] cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed border-green-300 bg-gray-100 p-4 hover:bg-green-50"
                >
                  <svg
                    className="mb-2 h-10 w-10 text-green-500"
                    fill="currentColor"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" />
                  </svg>
                  <span id="resume-text" className="text-green-500">
                    Select File
                  </span>
                  <input
                    name="resume"
                    onChange={handleInputChange}
                    type="file"
                    id="resume-input"
                    className="hidden"
                    accept=".pdf,.doc,.docx"
                  />
                  <p id="resume-file-name" className="mt-2 hidden text-gray-700">
                    No file selected
                  </p>
                </label>
              </div>
            </div>
            <label className="form-label">Preffered Location</label>
            <select
              name="preferredLocation"
              onChange={handleInputChange}
              id="city-select"
              className="form-input form-select singleSelect w-full"
            >
              <option value="" disabled selected>
                Select your city
              </option>
              <option value="Bangalore">Bangalore</option>
              <option value="Hyderabad">Hyderabad</option>
              <option value="Chennai">Chennai</option>
              <option value="Delhi">Delhi</option>
            </select>
            {/* <Select variant="standard" label="Select Version">
        <Option>Horizon UI HTML</Option>
        <Option>Horizon UI React</Option>
        <Option>Horizon UI Vue</Option>
        <Option>Horizon UI Angular</Option>
        <Option>Horizon UI Svelte</Option>
       </Select> */}
          </>
        )}
        {/* Continue with other steps and fields similarly */}

        <div className="mt-8 flex justify-between">
          {currentStep > 0 && (
            <button
              type="button"
              onClick={handlePrevious}
              className="rounded-md bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
            >
              Back
            </button>
          )}
          {currentStep < steps.length - 1 ? (
            <button
              type="button"
              onClick={handleNext}
              className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              Next
            </button>
          ) : (
            <button
              type="submit"
              className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              Submit
            </button>
          )}
          <Link to={"/create-account/employer"}>
            <span className="cursor-pointer text-blue-500 hover:underline">
              Sign up as a employeer
            </span>
          </Link>
        </div>
      </form>
    </div>
  );
}
