export function googleSignIn() {
  const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
  return new Promise((resolve, reject) => {
    // Load the Google Identity Services SDK if not already loaded
    if (!window.google || !window.google.accounts || !window.google.accounts.id) {
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => startSignIn();
      script.onerror = () => reject(new Error('Failed to load Google SDK'));
      document.body.appendChild(script);
    } else {
      startSignIn();
    }

    function startSignIn() {
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: handleCredentialResponse,
      });
      window.google.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          reject(new Error('Google Sign-In was cancelled or failed to display.'));
        }
      });
    }

    function handleCredentialResponse(response) {
      if (response.credential) {
        // Decode JWT to get email (optional, for display)
        let email;
        try {
          const payload = JSON.parse(atob(response.credential.split('.')[1]));
          email = payload.email;
        } catch (e) {
          email = undefined;
        }
        resolve({ token: response.credential, email });
      } else {
        reject(new Error('No credential received from Google.'));
      }
    }
  });
}
// Classic Google OAuth 2.0 Sign-In (popup-based, not One Tap/FedCM)
export function googleOAuthPopup(signInCallback) {
  const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
  const redirectUri = window.location.origin + "/google-oauth-callback.html";
  const scope = "email profile openid";
  const state = Math.random().toString(36).substring(2);
  const oauthUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${encodeURIComponent(clientId)}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=token id_token&scope=${encodeURIComponent(scope)}&state=${state}&prompt=select_account&nonce=${state}`;

  const width = 500, height = 600;
  const left = (window.screen.width / 2) - (width / 2);
  const top = (window.screen.height / 2) - (height / 2);

  const popup = window.open(
    oauthUrl,
    "google_oauth_popup",
    `width=${width},height=${height},top=${top},left=${left}`
  );

  // Listen for a message from the popup
  function receiveMessage(event) {
    if (
      event.origin === window.location.origin &&
      event.data &&
      event.data.type === "google_oauth_token"
    ) {
      if (popup) popup.close();
      window.removeEventListener("message", receiveMessage);
      signInCallback({
        accessToken: event.data.accessToken,
        idToken: event.data.idToken
      });
    }
  }
  window.addEventListener("message", receiveMessage);
}